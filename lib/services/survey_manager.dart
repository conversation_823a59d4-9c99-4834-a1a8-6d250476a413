import 'package:thedreamdeus/services/auth_service.dart';

class SurveyManager {
  static final SurveyManager _instance = SurveyManager._internal();
  factory SurveyManager() => _instance;
  SurveyManager._internal();

  final AuthService _authService = AuthService();
  
  // Survey cevaplarını geçici olarak tutmak için
  final Map<String, String> _surveyAnswers = {};

  // Cevap kaydet
  void saveAnswer(String question, String answer) {
    print('SurveyManager: Saving answer - Q: $question, A: $answer');
    _surveyAnswers[question] = answer;
    print('SurveyManager: Total answers saved: ${_surveyAnswers.length}');
  }

  // Cevap al
  String? getAnswer(String question) {
    return _surveyAnswers[question];
  }

  // Tüm cevapları Firestore'a kaydet
  Future<void> saveAllAnswersToFirestore() async {
    print('SurveyManager: Saving ${_surveyAnswers.length} answers to Firestore');
    for (String question in _surveyAnswers.keys) {
      String answer = _surveyAnswers[question]!;
      print('SurveyManager: Saving to Firestore - Q: $question, A: $answer');
      await _authService.saveSurveyAnswer(question, answer);
    }
    print('SurveyManager: All answers saved to Firestore');
    // Geçici cevapları temizle
    _surveyAnswers.clear();
  }

  // Geçici cevapları temizle
  void clearAnswers() {
    _surveyAnswers.clear();
  }

  // Tüm cevapları al
  Map<String, String> getAllAnswers() {
    return Map.from(_surveyAnswers);
  }
}
