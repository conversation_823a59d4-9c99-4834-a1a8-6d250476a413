import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:io' show Platform;

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Current user
  User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign up with email and password
  Future<UserCredential?> signUpWithEmailAndPassword(
      String email, String password) async {
    try {
      UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      // Create user document in Firestore
      await _createUserDocument(result.user!);
      
      return result;
    } catch (e) {
      print('Sign up error: $e');
      return null;
    }
  }

  // Sign in with email and password
  Future<UserCredential?> signInWithEmailAndPassword(
      String email, String password) async {
    try {
      UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return result;
    } catch (e) {
      print('Sign in error: $e');
      return null;
    }
  }

  // Sign in with Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) return null;

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      UserCredential result = await _auth.signInWithCredential(credential);

      // Create user document if new user
      await _createUserDocument(result.user!);

      return result;
    } catch (e) {
      print('Google sign in error: $e');
      return null;
    }
  }

  // Create user document in Firestore
  Future<void> _createUserDocument(User user) async {
    try {
      print('Creating user document for: ${user.uid}');
      DocumentSnapshot userDoc = await _firestore
          .collection('users')
          .doc(user.uid)
          .get();

      if (!userDoc.exists) {
        print('User document does not exist, creating new one...');
        await _firestore.collection('users').doc(user.uid).set({
          'uid': user.uid,
          'email': user.email,
          'displayName': user.displayName,
          'photoURL': user.photoURL,
          'createdAt': FieldValue.serverTimestamp(),
          'surveyAnswers': [], // Array for survey answers
        });
        print('User document created successfully!');
      } else {
        print('User document already exists');
      }
    } catch (e) {
      print('Error creating user document: $e');
    }
  }

  // Save survey answer
  Future<void> saveSurveyAnswer(String question, String answer) async {
    try {
      if (currentUser != null) {
        print('AuthService: Saving survey answer for user: ${currentUser!.uid}');
        print('AuthService: Question: $question, Answer: $answer');
        await _firestore.collection('users').doc(currentUser!.uid).update({
          'surveyAnswers': FieldValue.arrayUnion([
            {
              'question': question,
              'answer': answer,
              'timestamp': DateTime.now().millisecondsSinceEpoch,
            }
          ])
        });
        print('AuthService: Survey answer saved successfully!');
      } else {
        print('AuthService: No current user, cannot save survey answer');
      }
    } catch (e) {
      print('Error saving survey answer: $e');
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      await _auth.signOut();
    } catch (e) {
      print('Sign out error: $e');
    }
  }

  // Check if platform is iOS (for Apple Sign In)
  bool get isIOS => Platform.isIOS;

  // Check if user has completed survey
  Future<bool> hasCompletedSurvey() async {
    try {
      if (currentUser != null) {
        DocumentSnapshot userDoc = await _firestore
            .collection('users')
            .doc(currentUser!.uid)
            .get();

        if (userDoc.exists) {
          Map<String, dynamic> userData = userDoc.data() as Map<String, dynamic>;
          List<dynamic> surveyAnswers = userData['surveyAnswers'] ?? [];
          // Eğer 16 cevap varsa anket tamamlanmış
          return surveyAnswers.length >= 16;
        }
      }
      return false;
    } catch (e) {
      print('Error checking survey completion: $e');
      return false;
    }
  }

  // Test Firestore connection
  Future<void> testFirestoreConnection() async {
    try {
      print('Testing Firestore connection...');
      await _firestore.collection('test').doc('test').set({
        'message': 'Hello Firestore!',
        'timestamp': FieldValue.serverTimestamp(),
      });
      print('Firestore test successful!');
    } catch (e) {
      print('Firestore test failed: $e');
    }
  }
}
