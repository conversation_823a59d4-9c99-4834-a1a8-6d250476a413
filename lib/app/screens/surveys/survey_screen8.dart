import 'package:flutter/material.dart';
import 'package:thedreamdeus/app/screens/surveys/survey_screen9.dart';
import 'package:thedreamdeus/services/survey_manager.dart';

class SurveyScreen8 extends StatefulWidget {
  const SurveyScreen8({super.key});

  @override
  _SurveyScreen8State createState() => _SurveyScreen8State();
}

class _SurveyScreen8State extends State<SurveyScreen8> {
  String _selectedOption = '';
  final SurveyManager _surveyManager = SurveyManager();
  final String _question = 'Do you dream in color or black and white?';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/bg.jpg'),
                fit: BoxFit.cover,
              ),
            ),
          ),
          SafeArea(
            child: Column(
              children: [
                Align(
                  alignment: Alignment.topLeft,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                        child: const CircleAvatar(
                          backgroundColor: Colors.transparent,
                          child: Icon(Icons.arrow_back, color: Colors.white),
                        ),
                      ),
                    ),
                  ),
                ),
                const Opacity(
                  opacity: 0.6,
                  child: Image(
                    image: AssetImage('assets/images/iconlogo.png'),
                    height: 50,
                  ),
                ),
                const SizedBox(height: 16),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32.0),
                  child: Text(
                    'How do you think insomnia, stress or other factors affect your dreams?',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.5),
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32.0),
                  child: Column(
                    children: [
                      _buildOptionButton(context, 'My dreams become more complex.'),
                      const SizedBox(height: 16),
                      _buildOptionButton(context, 'I experience more nightmares'),
                      const SizedBox(height: 16),
                      _buildOptionButton(context, 'My dreams become more realistic and clearer'),
                      const SizedBox(height: 16),
                      _buildOptionButton(context, 'I wake up more frequently and struggle to recall my dreams'),
                      const SizedBox(height: 16),
                      _buildOptionButton(context, 'Nothing happens'),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    if (_selectedOption.isNotEmpty) {
                      _surveyManager.saveAnswer(_question, _selectedOption);
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => SurveyScreen9()),
                      );
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Please select an option')),
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.deepPurple,
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 120),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child: const Text(
                    'Next',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                    ),
                  ),
                ),
                const Spacer(),
                const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text(
                    'Dear user: accurate answering of the above information is crucial for the application to provide correct results and for scientific research.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionButton(BuildContext context, String text) {
    return SizedBox(
      width: double.infinity,
      height: 90,
      child: ElevatedButton(
        onPressed: () {
          setState(() {
            _selectedOption = text;
          });
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: _selectedOption == text
              ? Colors.deepPurple.withOpacity(0.5)
              : const Color(0xFF191919).withOpacity(0.5),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 40),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        child: Text(
          text,
          style: const TextStyle(
            fontSize: 18,
          ),
        ),
      ),
    );
  }
}