import 'package:flutter/material.dart';
import 'package:thedreamdeus/services/survey_manager.dart';
import 'package:thedreamdeus/app/screens/surveys/survey_screen5.dart';
import 'package:thedreamdeus/app/screens/surveys/survey_screen7.dart';

class SurveyScreen16 extends StatefulWidget {
  const SurveyScreen16({super.key});

  @override
  _SurveyScreen16State createState() => _SurveyScreen16State();
}

class _SurveyScreen16State extends State<SurveyScreen16> {
  String _selectedOption = '';
  final SurveyManager _surveyManager = SurveyManager();
  final String _question = 'Ever had déjà vu that felt like a dream?';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/bg.jpg'),
                fit: BoxFit.cover,
              ),
            ),
          ),
          SafeArea(
            child: Column(
              children: [
                Align(
                  alignment: Alignment.topLeft,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                        child: const CircleAvatar(
                          backgroundColor: Colors.transparent,
                          child: Icon(Icons.arrow_back, color: Colors.white),
                        ),
                      ),
                    ),
                  ),
                ),
                const Opacity(
                  opacity: 0.6,
                  child: Image(
                    image: AssetImage('assets/images/iconlogo.png'),
                    height: 50,
                  ),
                ),
                const SizedBox(height: 16), // Logo ve text arasındaki boşluk
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32.0),
                  child: Text(
                    'Ever had déjà vu that felt like a dream?',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.5),
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32.0),
                  child: Column(
                    children: [
                      _buildOptionButton(context, 'Yes'),
                      const SizedBox(height: 16),
                      _buildOptionButton(context, "I can't remember"),
                      const SizedBox(height: 16),
                      _buildOptionButton(context, "No"),

                    ],
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: _finishSurvey,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.deepPurple,
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 120),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child: const Text(
                    'Finish Survey',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                    ),
                  ),
                ),
                const Spacer(),
                const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text(
                    'Dear user: accurate answering of the above information is crucial for the application to provide correct results and for scientific research.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Anketi bitir ve tüm cevapları kaydet
  Future<void> _finishSurvey() async {
    if (_selectedOption.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select an option')),
      );
      return;
    }

    // Son cevabı kaydet
    _surveyManager.saveAnswer(_question, _selectedOption);

    // Loading dialog göster
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Text('Saving your responses...'),
            ],
          ),
        );
      },
    );

    // Tüm cevapları Firestore'a kaydet
    try {
      await _surveyManager.saveAllAnswersToFirestore();

      // Loading dialog'u kapat
      Navigator.of(context).pop();

      // Başarı mesajı göster
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Survey completed successfully!'),
          backgroundColor: Colors.green,
        ),
      );

      // Ana sayfaya dön
      Navigator.popUntil(context, (route) => route.isFirst);

    } catch (e) {
      // Loading dialog'u kapat
      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Error saving survey. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildOptionButton(BuildContext context, String text) {
    return SizedBox(
      width: double.infinity,
      height: 90,
      child: ElevatedButton(
        onPressed: () {
          setState(() {
            _selectedOption = text;
          });
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: _selectedOption == text
              ? Colors.deepPurple.withOpacity(0.5)
              : const Color(0xFF191919).withOpacity(0.5),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 40),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        child: Text(
          text,
          style: const TextStyle(
            fontSize: 18,
          ),
        ),
      ),
    );
  }
}
